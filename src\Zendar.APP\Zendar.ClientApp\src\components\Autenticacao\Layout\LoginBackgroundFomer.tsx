import { Flex, FlexProps, Text } from '@chakra-ui/react';
import React, { useEffect, useState } from 'react';

import { getImageBySystem } from 'helpers/layout/getImageBySystem';
import useWindowSize from 'helpers/layout/useWindowSize';

import Logo from 'components/Autenticacao/Logo';

import { loginBackgroundImgs } from 'constants/enum/enumsImgSistemas';

interface BackgroundProps extends FlexProps {
  children: React.ReactNode;
}

export const LoginBackgroundFomer = ({
  children,
  ...rest
}: BackgroundProps) => {
  const { height: windowHeight } = useWindowSize();

  const url = getImageBySystem(loginBackgroundImgs) || '';
  useEffect(() => {
    document.documentElement.style.setProperty(
      '--vh-autenticacao',
      `${windowHeight * 0.01}px`
    );
  }, [windowHeight]);

  return (
    <Flex
      position="relative"
      h="100%"
      w="full"
      minH="calc(var(--vh-autenticacao, 1vh) * 100)"
      justifyContent="center"
      alignItems="center"
      bgImage={`url(${url})`}
      bgPosition="center"
      bgRepeat="no-repeat"
      bgSize="cover"
      px={14}
      {...rest}
    >
      <Flex
        flexDirection="column"
        justifyContent="center"
        h="full"
        minH="calc(var(--vh-autenticacao, 1vh) * 100)"
        w={{ base: '265px', sm: '300px', xl: '350px' }}
        zIndex="docked"
        pt={{ base: '0', sm: '8', xl: '8' }}
        pb={{ base: '4', sm: '8', xl: '8' }}
      >
        <Logo />
        {children}
      </Flex>
      <Flex position="absolute" w="full" justifyContent="center" bottom="0">
        <Text color="purple.100">v{import.meta.env.VITE_APP_VERSION}</Text>
      </Flex>
    </Flex>
  );
};
